<template>
	<view>
		<cu-custom :bgColor="NavBarColor" isBack>
			<block slot="backText">返回</block>
			<block slot="content">签到</block>
		</cu-custom>

		<!-- 签到主界面 -->
		<view class="sign-in-container">
			<!-- 时间显示区域 -->
			<view class="time-section">
				<view class="current-time">{{ currentTime }}</view>
				<view class="current-date">{{ currentDate }}</view>
				<!-- <view class="timezone-info">{{ timezoneText }}</view> -->
			</view>

			<!-- 位置信息区域 -->
			<view class="location-section">
				<view class="location-icon">
					<text class="cuIcon-locationfill text-blue"></text>
				</view>
				<view class="location-info">
					<view class="location-address" v-if="locationAddress">{{ locationAddress }}</view>
					<view class="location-loading" v-else>正在获取位置信息...</view>
					<!-- <map id="workTrackMap" :longitude="longitude" :latitude="latitude" :scale="15" :show-location="true"
						class="work-map"></map> -->
					<!-- <view class="location-accuracy" v-if="longitude">
						经度: {{ longitude }}
					</view>
					<view class="location-accuracy" v-if="latitude">
						纬度: {{ latitude }}
					</view>
					<view class="location-accuracy" v-if="locationAccuracy">
						精度: {{ locationAccuracy }}米
					</view> -->

					<!-- {{ res }} -->
				</view>
			</view>

			<!-- 地图显示区域 -->
			<view class="map-section">
				<view class="map-container">
					<map class="map-view" :longitude="longitude" :latitude="latitude" :scale="15" :show-location="true"
						:markers="markers" :enable-zoom="true" :enable-scroll="true" :enable-rotate="false">
					</map>
				</view>
			</view>

			<!-- 图片上传区域 -->
			<view class="photo-section">
				<view class="photo-title">
					<text class="cuIcon-camerafill text-blue"></text>
					<text class="photo-title-text">签到照片</text>
					<text class="photo-required">(必须)</text>
				</view>
				<view class="photo-upload-area">
					<view class="photo-list" v-if="uploadedImages.length > 0">
						<view class="photo-item" v-for="(image, index) in uploadedImages" :key="index">
							<image :src="image.tempPath" class="photo-image" mode="aspectFill"
								@tap="previewImage(image.tempPath)"></image>
							<view class="photo-delete" @tap="deleteImage(index)">
								<text class="cuIcon-close"></text>
							</view>
						</view>
					</view>
					<view class="photo-upload-btn" v-if="uploadedImages.length < maxImages" @tap="chooseImage">
						<text class="cuIcon-camera upload-icon"></text>
						<text class="upload-text">拍照上传</text>
						<text class="upload-limit">({{ uploadedImages.length }}/{{ maxImages }})</text>
					</view>
				</view>
			</view>

			<!-- 签到按钮区域 -->
			<view class="sign-button-section">
				<view class="button-group">
					<!-- 签到按钮 -->
					<button class="sign-in-button" :class="{ 'completed': todayAttendance.signInTime }"
						@tap="handleSignIn">
						<text class="cuIcon-enter button-icon"></text>
						<text class="button-text">签到</text>
					</button>

					<!-- 签退按钮 -->
					<button class="sign-out-button" :class="{ 'completed': todayAttendance.signOutTime }"
						@tap="handleSignOut">
						<text class="cuIcon-exit button-icon"></text>
						<text class="button-text">签退</text>
					</button>
				</view>
			</view>

			<!-- 今日签到状态 -->
			<view class="attendance-status-section">
				<view class="status-title">
					<text class="cuIcon-calendar text-blue"></text>
					<text class="status-title-text">今日考勤状态</text>
				</view>
				<view class="status-content">
					<view class="status-date">{{ currentDate }}</view>
					<view class="status-items">
						<view class="status-item">
							<view class="status-label">签到时间</view>
							<view class="status-value-wrapper">
								<view class="status-value" :class="{ 'has-value': todayAttendance.signInTime }">
									{{ todayAttendance.signInTime || '未签到' }}
								</view>
								<view class="attendance-photo-list">
									<image v-for="(photo, index) in todayAttendance.signInPhotos" :key="index"
										:src="photo" class="attendance-photo" mode="aspectFill"
										@tap="previewAttendanceImage(photo, todayAttendance.signInPhotos)"></image>
								</view>
							</view>
						</view>
						<view class="status-item">
							<view class="status-label">签退时间</view>
							<view class="status-value-wrapper">
								<view class="status-value" :class="{ 'has-value': todayAttendance.signOutTime }">
									{{ todayAttendance.signOutTime || '未签退' }}
								</view>
								<view class="attendance-photo-list">
									<image v-for="(photo, index) in todayAttendance.signOutPhotos" :key="index"
										:src="photo" class="attendance-photo" mode="aspectFill"
										@tap="previewAttendanceImage(photo, todayAttendance.signOutPhotos)"></image>
								</view>
							</view>
						</view>
						<view class="status-item" v-if="workDuration">
							<view class="status-label">工作时长</view>
							<view class="status-value has-value">{{ workDuration }}</view>
						</view>
					</view>
					<view class="status-summary">
						<text class="status-badge" :class="getStatusClass()">{{ todayAttendance.status || '未签到'
						}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import configService from '@/common/service/config.service.js';
export default {
	data() {
		return {
			// 时间相关
			currentTime: '',
			currentDate: '',
			timezoneText: '当前时区：上海 (签到时间 6:00-22:00)',
			timeTimer: null,
			isXinjiang: false,

			// 位置相关
			locationAddress: '',
			locationAccuracy: null,
			latitude: '',
			longitude: '',
			amapPlugin: null,
			wxMapKey: "53324ee357405c4a65f35a1aa05ffaf2",

			// 地图相关
			markers: [],

			// 签到相关
			canSignIn: false,
			canSignOut: false,
			signButtonText: '签到',
			statusMessage: '',
			statusType: 'info',

			// 今日考勤状态
			todayAttendance: {
				signInTime: null,
				signOutTime: null,
				status: '未签到',
				signInPhotos: [],
				signOutPhotos: []
			},
			scan: '',

			// 图片上传相关
			uploadedImages: [],
			maxImages: 3,

			// 工作时长
			workDuration: '',

			res: {},
		}
	},
	methods: {
		screen() {
			const that = this
			this.$wx.scanQRCode({
				desc: 'scanQRCode desc',
				needResult: 1,
				scanType: ["qrCode", "barCode"],
				success: function (res) {
					console.log("🚀 ~ screen ~ res:", res)
					that.scan = res.resultStr
				},
				error: function (res) {
					if (res.errMsg.indexOf('function_not_exist') > 0) {
						alert('版本过低请升级')
					}
				}
			});
		},
		// 初始化时间显示
		initTime() {
			this.updateTime();
			this.timeTimer = setInterval(() => {
				this.updateTime();
			}, 1000);
		},

		// 更新时间显示
		updateTime() {
			const now = new Date();
			let displayTime;

			if (this.isXinjiang) {
				// 新疆时间 (UTC+6)
				const xinjiangTime = new Date(now.getTime() - 2 * 60 * 60 * 1000);
				displayTime = xinjiangTime;
				this.timezoneText = '当前区域：新疆 (签到时间 6:00-22:00)';
			} else {
				// 上海时间 (UTC+8)
				displayTime = now;
				this.timezoneText = '当前区域：上海 (签到时间 6:00-22:00)';
			}

			// 格式化时间
			this.currentTime = this.formatTime(displayTime);
			this.currentDate = this.formatDate(displayTime);
		},

		// 获取图片URL
		getImageUrl(imagePath) {
			if (!imagePath) return '';
			return apiService.getFileAccessHttpUrl(imagePath);
		},
		// 格式化时间 HH:MM:SS
		formatTime(date) {
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${hours}:${minutes}:${seconds}`;
		},

		// 格式化日期
		formatDate(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
			const weekday = weekdays[date.getDay()];
			return `${year}年${month}月${day}日 ${weekday}`;
		},



		// 获取位置信息
		getLocationInfo() {
			const that = this
			// if (this.$wx) {
			// 	this.$wx.getLocation({
			// 		type: 'gcj02',
			// 		success: (res) => {
			// 			that.res = res
			// 			that.latitude = res.latitude;
			// 			that.longitude = res.longitude;
			// 			that.locationAccuracy = Math.round(res.accuracy);

			// 			// 更新地图标记点
			// 			that.updateMapMarkers();

			// 			that.reverseGeocode();
			// 		},
			// 		fail: (error) => {
			// 			console.error('获取位置失败：', error);
			// 			this.statusMessage = '获取位置信息失败，请检查定位权限';
			// 			this.statusType = 'error';
			// 		}
			// 	});
			// }
			console.log('当前位置的经度：');
			uni.getLocation({
				type: 'wgs84',
				success: function (res) {
					console.log('当前位置的经度：' + res.longitude);
					console.log('当前位置的纬度：' + res.latitude);

					// 调用高德地图api
					getFormattedAddress(res.latitude, res.longitude)
				}
			});
		},

		// 逆地址解析
		reverseGeocode() {
			this.$http.get("/pm/wx/getAddress", {
				params: {
					longitude: this.longitude,
					latitude: this.latitude
				}
			}).then(res => {
				if (res.data && res.data.result && res.data.result.regeocode) {
					this.locationAddress = res.data.result.regeocode.formatted_address;
					this.checkIfXinjiang(this.locationAddress);
				} else {
					this.locationAddress = '位置解析失败';
				}
				// 更新签到状态
				this.updateSignInStatus();
			}).catch(error => {
				console.error('逆地址解析失败：', error);
				this.locationAddress = '位置获取失败';
				// 仍然更新签到状态，不因为地址解析失败而阻止签到
				this.updateSignInStatus();
			});
		},
		checkIfXinjiang(locationResult) {
			if (locationResult.indexOf('新疆') > -1) {
				this.isXinjiang = true;

				this.updateTime();
			}
		},

		// 更新地图标记点
		updateMapMarkers() {
			if (this.latitude && this.longitude) {
				this.markers = [{
					id: 1,
					latitude: this.latitude,
					longitude: this.longitude,
					title: '当前位置',
					width: 30,
					height: 30,
					callout: {
						content: '我的位置',
						color: '#ffffff',
						fontSize: 12,
						borderRadius: 4,
						bgColor: '#007AFF',
						padding: 8,
						display: 'ALWAYS'
					}
				}];
			}
		},

		// 更新签到状态
		updateSignInStatus() {
			// 获取今日考勤状态
			this.checkTodayAttendance();

			// 设置按钮状态 - 随时都可以签到签退
			this.canSignIn = true;   // 签到按钮始终可用
			this.canSignOut = true;  // 签退按钮始终可用
		},

		// 检查今日考勤状态
		checkTodayAttendance() {
			// 调用API获取今日考勤状态
			this.getTodayAttendanceFromAPI();
		},
		getFullImageUrl(url) {
			if (!url) return '';
			if (url.startsWith('http')) {
				return url;
			}
			const domain = configService.staticDomainURL || '';
			// 移除 domain 尾部的 /
			const cleanDomain = domain.endsWith('/') ? domain.slice(0, -1) : domain;
			// 移除 url 头部的 /
			const cleanUrl = url.startsWith('/') ? url.slice(1) : url;

			if (!cleanDomain) return `/${cleanUrl}`;

			return `${cleanDomain}/${cleanUrl}`;
		},


		// 从API获取今日考勤状态
		getTodayAttendanceFromAPI() {
			// 显示加载状态
			uni.showLoading({
				title: '获取考勤状态...',
				mask: true
			});
			// 调用获取考勤列表的API
			this.$http.get('/pm/clockIn/list', {}).then(res => {
				console.log('获取今日考勤状态成功：', res);
				if (res.data && res.data.success) {
					// 分别找出签到和签退记录
					const signInRecord = res.data.result.find(record => record.type === 1);
					const signOutRecord = res.data.result.find(record => record.type === 2);

					// 更新考勤状态
					this.todayAttendance = {
						signInTime: signInRecord ? this.extractTime(signInRecord.checkInTime) : null,
						signOutTime: signOutRecord ? this.extractTime(signOutRecord.checkInTime) : null,
						status: this.getAttendanceStatus(signInRecord, signOutRecord),
						signInPhotos: signInRecord && signInRecord.pic ? signInRecord.pic.split(',').map(p => getFullImageUrl(p)) : [],
						signOutPhotos: signOutRecord && signOutRecord.pic ? signOutRecord.pic.split(',').map(p => getFullImageUrl(p)) : []
					};

					// 清空照片列表
					this.uploadedImages = [];

					// 计算工作时长
					this.calculateWorkDuration();

					console.log('今日考勤状态：', this.todayAttendance);
				} else {
					console.error('获取考勤状态失败：', res.data?.message || '未知错误');
					// 重置考勤状态
					this.todayAttendance = {
						signInTime: null,
						signOutTime: null,
						status: '未签到',
						signInPhotos: [],
						signOutPhotos: []
					};
					this.workDuration = '';
				}
			}).catch(error => {
				console.error('获取今日考勤状态失败：', error);
				uni.showToast({
					title: '网络异常，请重试',
					icon: 'none'
				});
				// 重置考勤状态
				this.todayAttendance = {
					signInTime: null,
					signOutTime: null,
					status: '未签到',
					signInPhotos: [],
					signOutPhotos: []
				};
				this.workDuration = '';
			}).finally(() => {
				uni.hideLoading();
			});
		},

		// 从完整时间字符串中提取时间部分 HH:MM:SS
		extractTime(dateTimeStr) {
			if (!dateTimeStr) return null;
			const timePart = dateTimeStr.split(' ')[1]; // 获取时间部分
			return timePart || null;
		},

		// 根据签到签退记录获取考勤状态
		getAttendanceStatus(signInRecord, signOutRecord) {
			try {
				if (signInRecord && signOutRecord) {
					// 都有记录，检查状态
					const statusParts = [];
					if (signInRecord.status === '迟到') statusParts.push('迟到');
					if (signOutRecord.status === '早退') statusParts.push('早退');

					if (statusParts.length > 0) {
						return `已签退 (${statusParts.join('、')})`;
					}
					return '已签退';
				} else if (signInRecord) {
					return signInRecord.status === '迟到' ? '已签到 (迟到)' : '已签到';
				} else {
					return '未签到';
				}
			} catch (error) {
				console.error('获取考勤状态失败：', error);
				return '未签到';
			}
		},

		// 计算工作时长
		calculateWorkDuration() {
			try {
				if (this.todayAttendance.signInTime && this.todayAttendance.signOutTime) {
					const signInTime = new Date(`2000-01-01 ${this.todayAttendance.signInTime}`);
					const signOutTime = new Date(`2000-01-01 ${this.todayAttendance.signOutTime}`);

					// 检查时间是否有效
					if (isNaN(signInTime.getTime()) || isNaN(signOutTime.getTime())) {
						this.workDuration = '';
						return;
					}

					const diffMs = signOutTime.getTime() - signInTime.getTime();

					// 如果时间差为负数，说明数据有问题
					if (diffMs < 0) {
						this.workDuration = '';
						return;
					}

					const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
					const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

					if (diffHours > 0) {
						this.workDuration = `${diffHours}小时${diffMinutes > 0 ? diffMinutes + '分钟' : ''}`;
					} else if (diffMinutes > 0) {
						this.workDuration = `${diffMinutes}分钟`;
					} else {
						this.workDuration = '不足1分钟';
					}
				} else {
					this.workDuration = '';
				}
			} catch (error) {
				console.error('计算工作时长失败：', error);
				this.workDuration = '';
			}
		},

		// 获取状态样式类
		getStatusClass() {
			const status = this.todayAttendance.status;
			if (status === '已签退') {
				return 'status-completed';
			} else if (status === '已签到') {
				return 'status-signed-in';
			} else {
				return 'status-not-signed';
			}
		},
		// 检查签到签退时间是否在允许范围内
		checkTimeRange() {
			const now = new Date();
			const currentHour = now.getHours();

			// 允许时间范围：6:00 - 22:00
			if (currentHour < 6 || currentHour >= 22) {
				return false;
			}
			return true;
		},

		// 处理签到
		handleSignIn() {
			// 检查时间范围
			if (!this.checkTimeRange()) {
				uni.showToast({
					title: '签到时间限制在早6:00到晚22:00之间',
					icon: 'none',
					duration: 2000
				});
				return;
			}

			// 检查位置信息
			if (!this.latitude || !this.longitude) {
				uni.showToast({
					title: '请等待位置信息获取完成',
					icon: 'none'
				});
				return;
			}

			// 检查是否上传了照片
			if (this.uploadedImages.length === 0) {
				uni.showToast({
					title: '签到必须上传照片',
					icon: 'none',
					duration: 2000
				});
				return;
			}

			// 提交签到数据 (type: 1)
			this.submitAttendance(1).then(() => {
				// 更新本地状态
				const now = new Date();
				this.todayAttendance.signInTime = this.formatTime(now);
				this.todayAttendance.status = '已签到';

				// 显示成功提示
				uni.showToast({
					title: '签到成功',
					icon: 'success',
					duration: 1000
				});

				// 1秒后重新获取最新的考勤状态
				setTimeout(() => {
					this.getTodayAttendanceFromAPI();
				}, 1000);
			}).catch(() => {
				// 提交失败的处理在submitAttendance方法中已经处理
			});
		},

		// 处理签退
		handleSignOut() {
			// 检查时间范围
			if (!this.checkTimeRange()) {
				uni.showToast({
					title: '签退时间限制在早6:00到晚22:00之间',
					icon: 'none',
					duration: 2000
				});
				return;
			}

			// 检查位置信息
			if (!this.latitude || !this.longitude) {
				uni.showToast({
					title: '请等待位置信息获取完成',
					icon: 'none'
				});
				return;
			}

			// 检查是否上传了照片
			if (this.uploadedImages.length === 0) {
				uni.showToast({
					title: '签退必须上传照片',
					icon: 'none',
					duration: 2000
				});
				return;
			}

			// 提交签退数据 (type: 2)
			this.submitAttendance(2).then(() => {
				// 更新本地状态
				const now = new Date();
				this.todayAttendance.signOutTime = this.formatTime(now);
				this.todayAttendance.status = '已签退';

				// 显示成功提示
				uni.showToast({
					title: '签退成功',
					icon: 'success',
					duration: 1000
				});

				// 1秒后重新获取最新的考勤状态
				setTimeout(() => {
					this.getTodayAttendanceFromAPI();
				}, 1000);
			}).catch(() => {
			});
		},

		// 提交考勤数据
		submitAttendance(type) {
			// 构建提交数据
			const clockInData = {
				clockIn: {
					type: type, // 1: 签到, 2: 签退
					longitude: this.longitude ? this.longitude.toString() : "",
					latitude: this.latitude ? this.latitude.toString() : "",
					pic: this.uploadedImages.length > 0 ? this.uploadedImages.map(item => item.url).join(',') : ""
				}
			};


			// 调用签到接口
			return this.$http.post('/pm/clockIn/add', clockInData)
				.then(response => {
					if (response.data.code == 200) {
						uni.showToast({
							title: response.data.message,
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: response.data.message,
							icon: 'error'
						});
					}
					return response;
				})
				.catch(error => {
					console.error('考勤提交失败', error);
					uni.showToast({
						title: '提交失败，请重试',
						icon: 'none'
					});
					throw error;
				});
		},

		// 选择图片
		chooseImage() {
			const that = this;
			uni.chooseImage({
				count: this.maxImages - this.uploadedImages.length,
				sizeType: ['compressed'], // 压缩图片
				sourceType: ['camera'], // 相机和相册
				success: (res) => {
					console.log('选择图片成功：', res);
					// 上传选中的图片
					res.tempFilePaths.forEach((tempPath) => {
						that.uploadImage(tempPath);
					});
				},
				fail: (error) => {
					console.error('选择图片失败：', error);
					uni.showToast({
						title: '选择图片失败',
						icon: 'none'
					});
				}
			});
		},

		// 上传图片
		uploadImage(tempFilePath) {
			const that = this;
			// 显示上传进度
			uni.showLoading({
				title: '上传中...'
			});
			// 调用系统上传接口
			uni.uploadFile({
				url: configService.apiUrl + '/sys/obs/upload',
				filePath: tempFilePath,
				name: 'file',
				header: {
					// 可以添加认证头等
					'X-Access-Token': uni.getStorageSync('Access-Token') || ''
				},
				success: (uploadRes) => {
					console.log('上传成功：', uploadRes);
					try {
						const result = JSON.parse(uploadRes.data);
						if (result.success) {
							// 根据实际返回的数据结构调整
							const imageUrl = result.result.objectKey;
							that.uploadedImages.push({
								url: imageUrl,
								tempPath: tempFilePath
							});
							console.log("🚀 ~ uploadImage ~ that.uploadedImages:", that.uploadedImages)

							uni.showToast({
								title: '上传成功',
								icon: 'success'
							});
						} else {
							throw new Error(result.message || '上传失败');
						}
					} catch (error) {
						console.error('解析上传结果失败：', error);
						uni.showToast({
							title: '上传失败',
							icon: 'none'
						});
					}
				},
				fail: (error) => {
					console.error('上传失败：', error);
					uni.showToast({
						title: '上传失败，请重试',
						icon: 'none'
					});
				},
				complete: () => {
					uni.hideLoading();
				}
			});
		},

		// 删除图片
		deleteImage(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这张照片吗？',
				success: (res) => {
					if (res.confirm) {
						this.uploadedImages.splice(index, 1);
						uni.showToast({
							title: '删除成功',
							icon: 'success'
						});
					}
				}
			});
		},

		// 预览图片
		previewImage(currentUrl) {
			const urls = this.uploadedImages.map(img => img.tempPath);
			uni.previewImage({
				current: currentUrl,
				urls: urls
			});
		},

		previewAttendanceImage(currentUrl, photoList) {
			uni.previewImage({
				current: currentUrl,
				urls: photoList
			});
		}
	},

	computed: {
	},

	onLoad() {
		// 初始化时间显示
		this.initTime();

		// 获取位置信息
		this.getLocationInfo();

		// 初始化签到状态
		this.updateSignInStatus();
	},

	onUnload() {
		// 清除定时器
		if (this.timeTimer) {
			clearInterval(this.timeTimer);
		}
	}
}
</script>

<style scoped>
.sign-in-container {
	padding: 20upx;
	min-height: calc(100vh - 88upx);
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 时间显示区域 */
.time-section {
	background: #ffffff;
	border-radius: 20upx;
	padding: 40upx;
	margin-bottom: 30upx;
	text-align: center;
	box-shadow: 0 8upx 25upx rgba(0, 0, 0, 0.1);
}

.current-time {
	font-size: 72upx;
	font-weight: bold;
	color: #2c3e50;
	margin-bottom: 10upx;
	font-family: 'Helvetica Neue', Arial, sans-serif;
}

.current-date {
	font-size: 32upx;
	color: #7f8c8d;
	margin-bottom: 20upx;
}

.timezone-info {
	font-size: 28upx;
	color: #3498db;
	background: #ecf0f1;
	padding: 15upx 25upx;
	border-radius: 50upx;
	display: inline-block;
}

/* 位置信息区域 */
.location-section {
	background: #ffffff;
	border-radius: 20upx;
	padding: 30upx;
	margin-bottom: 30upx;
	display: flex;
	align-items: flex-start;
	box-shadow: 0 8upx 25upx rgba(0, 0, 0, 0.1);
}

.location-icon {
	margin-right: 20upx;
	margin-top: 5upx;
}

.location-icon text {
	font-size: 40upx;
}

.location-info {
	flex: 1;
}

.location-address {
	font-size: 30upx;
	color: #2c3e50;
	line-height: 1.5;
	margin-bottom: 10upx;
}

.location-loading {
	font-size: 30upx;
	color: #95a5a6;
	line-height: 1.5;
}

.location-accuracy {
	font-size: 24upx;
	color: #7f8c8d;
}

/* 地图显示区域 */
.map-section {
	background: #ffffff;
	border-radius: 20upx;
	padding: 30upx;
	margin-bottom: 30upx;
	box-shadow: 0 8upx 25upx rgba(0, 0, 0, 0.1);
}

.map-title {
	display: flex;
	align-items: center;
	margin-bottom: 20upx;
}

.map-title text:first-child {
	font-size: 32upx;
	margin-right: 10upx;
}

.map-title-text {
	font-size: 30upx;
	color: #2c3e50;
	font-weight: 500;
}

.map-container {
	width: 100%;
	height: 400upx;
	border-radius: 12upx;
	overflow: hidden;
	border: 1upx solid #e9ecef;
}

.map-view {
	width: 100%;
	height: 100%;
}

/* 图片上传区域 */
.photo-section {
	background: #ffffff;
	border-radius: 20upx;
	padding: 30upx;
	margin-bottom: 30upx;
	box-shadow: 0 8upx 25upx rgba(0, 0, 0, 0.1);
}

.photo-title {
	display: flex;
	align-items: center;
	margin-bottom: 20upx;
}

.photo-title text:first-child {
	font-size: 32upx;
	margin-right: 10upx;
}

.photo-title-text {
	font-size: 30upx;
	color: #2c3e50;
	font-weight: 500;
}

.photo-required {
	font-size: 24upx;
	color: #ff4757;
	margin-left: 8upx;
	font-weight: 600;
}

.photo-upload-area {
	display: flex;
	flex-direction: column;
	gap: 15upx;
}

.photo-list {
	display: flex;
	flex-wrap: wrap;
	gap: 15upx;
	width: 100%;
	margin-bottom: 15upx;
}

.photo-item {
	position: relative;
	width: 150upx;
	height: 150upx;
	border-radius: 12upx;
	overflow: hidden;
}

.photo-image {
	width: 100%;
	height: 100%;
	border-radius: 12upx;
}

.photo-delete {
	position: absolute;
	top: -8upx;
	right: -8upx;
	width: 32upx;
	height: 32upx;
	background: #ff4757;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2upx 8upx rgba(255, 71, 87, 0.3);
}

.photo-delete text {
	color: #ffffff;
	font-size: 20upx;
}

.photo-upload-btn {
	width: 100%;
	height: 120upx;
	border: none;
	border-radius: 12upx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #ff9500;
	transition: all 0.3s ease;
	box-shadow: 0 4upx 12upx rgba(255, 149, 0, 0.3);
	margin-top: 20upx;
}

.photo-upload-btn:active {
	background: #e6850e;
	transform: translateY(2upx);
	box-shadow: 0 2upx 8upx rgba(255, 149, 0, 0.4);
}

.upload-icon {
	font-size: 36upx;
	color: #ffffff;
	margin-right: 12upx;
}

.upload-text {
	font-size: 28upx;
	color: #ffffff;
	font-weight: 600;
	margin-right: 8upx;
}

.upload-limit {
	font-size: 24upx;
	color: #ffffff;
	opacity: 0.9;
}

/* 签到按钮区域 */
.sign-button-section {
	text-align: center;
	margin-bottom: 30upx;
}

.button-group {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 30upx;
	margin-bottom: 20upx;
}

.attendance-photo {
	width: 100upx;
	height: 100upx;
	border-radius: 12upx;
	margin-top: 10upx;
}

.sign-in-button,
.sign-out-button {
	width: 200upx;
	height: 80upx;
	border-radius: 12upx;
	color: #ffffff;
	font-size: 28upx;
	font-weight: 600;
	border: none;
	box-shadow: 0 4upx 12upx rgba(0, 0, 0, 0.15);
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;
}

.sign-in-button {
	background: #3498db;
}

.sign-out-button {
	background: #2ecc71;
}

.sign-in-button:active,
.sign-out-button:active {
	transform: translateY(2upx);
	box-shadow: 0 2upx 8upx rgba(0, 0, 0, 0.2);
}

.sign-in-button.completed {
	background: #5dade2;
}

.sign-out-button.completed {
	background: #58d68d;
}

.button-icon {
	font-size: 32upx;
	margin-right: 8upx;
}

.button-text {
	font-size: 28upx;
	font-weight: 600;
}

.button-time {
	font-size: 20upx;
	opacity: 0.9;
	position: absolute;
	bottom: 5upx;
	left: 50%;
	transform: translateX(-50%);
	white-space: nowrap;
}

/* 今日考勤状态区域 */
.attendance-status-section {
	background: #ffffff;
	border-radius: 20upx;
	padding: 30upx;
	margin-bottom: 30upx;
	box-shadow: 0 8upx 25upx rgba(0, 0, 0, 0.1);
}

.status-title {
	display: flex;
	align-items: center;
	margin-bottom: 25upx;
	padding-bottom: 15upx;
	border-bottom: 1upx solid #f0f0f0;
}

.status-title text:first-child {
	font-size: 32upx;
	margin-right: 10upx;
}

.status-title-text {
	font-size: 30upx;
	color: #2c3e50;
	font-weight: 500;
}

.status-content {
	display: flex;
	flex-direction: column;
}

.status-date {
	font-size: 26upx;
	color: #7f8c8d;
	text-align: center;
	margin-bottom: 20upx;
}

.status-items {
	display: flex;
	flex-direction: column;
	gap: 15upx;
	margin-bottom: 20upx;
}

.status-item {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	padding: 15upx 20upx;
	background: #f8f9fa;
	border-radius: 12upx;
}

.status-label {
	font-size: 28upx;
	color: #495057;
	font-weight: 500;
}

.status-value {
	font-size: 28upx;
	color: #adb5bd;
	text-align: right;
}

.status-value-wrapper {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	flex: 1;
}

.status-value.has-value {
	color: #2c3e50;
	font-weight: 600;
}

.status-summary {
	text-align: center;
}

.status-badge {
	display: inline-block;
	padding: 12upx 24upx;
	border-radius: 50upx;
	font-size: 26upx;
	font-weight: 500;
}

.status-badge.status-not-signed {
	background: #e9ecef;
	color: #6c757d;
}

.status-badge.status-signed-in {
	background: #d1ecf1;
	color: #0c5460;
}

.status-badge.status-completed {
	background: #d4edda;
	color: #155724;
}

.attendance-photo-list {
	display: flex;
	flex-wrap: wrap;
	gap: 10upx;
	margin-top: 10upx;
	justify-content: flex-end;
}

.attendance-photo {
	width: 100upx;
	height: 100upx;
	border-radius: 12upx;
}

/* 响应式适配 */
@media screen and (max-width: 750upx) {
	.current-time {
		font-size: 60upx;
	}

	.sign-in-button,
	.sign-out-button {
		width: 160upx;
		height: 70upx;
		font-size: 24upx;
	}

	.button-group {
		gap: 20upx;
	}

	.button-icon {
		font-size: 28upx;
	}

	.photo-upload-btn {
		height: 100upx;
	}

	.upload-text {
		font-size: 24upx;
	}
}
</style>
